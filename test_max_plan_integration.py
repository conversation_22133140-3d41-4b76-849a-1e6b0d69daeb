#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 Max Plan 功能集成
验证新添加的 put-user-on-plan API 方法是否正确植入软件中
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_api_client_methods():
    """测试 APIClient 类中的新方法"""
    print("🧪 测试 APIClient 类中的新方法...")
    
    try:
        from team_manager import APIClient, Config
        
        # 创建配置和API客户端
        config = Config()
        api_client = APIClient(config)
        
        # 检查新方法是否存在
        methods_to_check = [
            'put_user_on_community_plan',
            'put_user_on_max_plan', 
            'put_user_on_plan'
        ]
        
        for method_name in methods_to_check:
            if hasattr(api_client, method_name):
                print(f"✅ 方法 {method_name} 存在")
                method = getattr(api_client, method_name)
                if callable(method):
                    print(f"✅ 方法 {method_name} 可调用")
                else:
                    print(f"❌ 方法 {method_name} 不可调用")
            else:
                print(f"❌ 方法 {method_name} 不存在")
        
        print("✅ APIClient 方法测试完成")
        return True
        
    except Exception as e:
        print(f"❌ APIClient 方法测试失败: {e}")
        return False

def test_worker_thread_operations():
    """测试 WorkerThread 类中的新操作"""
    print("\n🧪 测试 WorkerThread 类中的新操作...")
    
    try:
        from team_manager import WorkerThread, APIClient, Config
        
        # 创建配置和API客户端
        config = Config()
        api_client = APIClient(config)
        
        # 测试新操作
        operations_to_check = [
            'put_user_on_community_plan',
            'put_user_on_max_plan',
            'put_user_on_plan'
        ]
        
        for operation in operations_to_check:
            try:
                # 创建工作线程（不运行）
                worker = WorkerThread(api_client, operation)
                print(f"✅ 操作 {operation} 可以创建工作线程")
            except Exception as e:
                print(f"❌ 操作 {operation} 创建工作线程失败: {e}")
        
        print("✅ WorkerThread 操作测试完成")
        return True
        
    except Exception as e:
        print(f"❌ WorkerThread 操作测试失败: {e}")
        return False

def test_config_cookie_update():
    """测试配置中的 cookie 是否已更新"""
    print("\n🧪 测试配置中的 cookie 是否已更新...")
    
    try:
        from team_manager import Config
        
        config = Config()
        cookie = config.get('api.headers.cookie', '')
        
        # 检查是否包含新的 cookie 信息
        expected_parts = [
            'vector_visitor_id=16cd1864-ab50-42c7-9906-0c9e4db2574d',
            'ajs_user_id=ea900e8a-0a8b-4b3d-aa82-aefcd908f3fd',
            '_session=************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.4erGj14zvxI6oA7H8ecj6YnpCFrZTq1zRKHHpXVfhxY'
        ]
        
        found_parts = 0
        for part in expected_parts:
            if part in cookie:
                found_parts += 1
                print(f"✅ 找到 cookie 部分: {part[:50]}...")
            else:
                print(f"❌ 未找到 cookie 部分: {part[:50]}...")
        
        if found_parts >= 2:  # 至少找到2个关键部分
            print("✅ Cookie 配置已更新")
            return True
        else:
            print("❌ Cookie 配置可能未正确更新")
            return False
        
    except Exception as e:
        print(f"❌ Cookie 配置测试失败: {e}")
        return False

def test_ui_integration():
    """测试UI集成（不启动GUI）"""
    print("\n🧪 测试UI集成...")
    
    try:
        from team_manager import TeamManagerMainWindow
        
        # 检查主窗口类是否包含新方法
        methods_to_check = [
            'switch_to_max_plan'
        ]
        
        for method_name in methods_to_check:
            if hasattr(TeamManagerMainWindow, method_name):
                print(f"✅ UI方法 {method_name} 存在")
            else:
                print(f"❌ UI方法 {method_name} 不存在")
        
        print("✅ UI集成测试完成")
        return True
        
    except Exception as e:
        print(f"❌ UI集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试 Max Plan 功能集成...")
    print("=" * 60)
    
    tests = [
        test_api_client_methods,
        test_worker_thread_operations,
        test_config_cookie_update,
        test_ui_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！Max Plan 功能已成功集成到软件中。")
        print("\n📋 功能说明:")
        print("• 新增了 put_user_on_max_plan() API 方法")
        print("• 新增了通用的 put_user_on_plan(plan_id) API 方法")
        print("• 在批量操作标签页中添加了 '⭐ 切换到 Max 计划' 按钮")
        print("• 更新了配置文件中的 cookie 信息")
        print("• 支持异步操作，不会阻塞界面")
        print("\n🔧 使用方法:")
        print("1. 启动团队管理工具")
        print("2. 切换到 '批量操作' 标签页")
        print("3. 点击 '⭐ 切换到 Max 计划' 按钮")
        print("4. 在操作日志中查看结果")
    else:
        print("❌ 部分测试失败，请检查代码集成。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
