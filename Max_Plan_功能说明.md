# Max Plan 功能集成说明

## 概述

已成功将您提供的 `put-user-on-plan` API 方法植入到团队管理软件中，支持将登录账号切换到 Max Plan。

## 新增功能

### 1. API 方法
在 `APIClient` 类中新增了以下方法：

#### `put_user_on_max_plan()`
```python
def put_user_on_max_plan(self) -> <PERSON><PERSON>[bool, str]:
    """将登录账号改为 max plan"""
    try:
        url = f"{self.config.get('api.base_url')}/put-user-on-plan"
        headers = self._get_headers()
        data = {"planId": "orb_max_plan"}

        response = self.session.post(url, headers=headers, json=data, timeout=30)

        if response.status_code == 200:
            return True, "账号已成功切换到 Max Plan"
        else:
            return False, f"切换失败，状态码: {response.status_code}\n响应: {response.text}"
    except Exception as e:
        return False, f"网络错误: {str(e)}"
```

#### `put_user_on_plan(plan_id)`
```python
def put_user_on_plan(self, plan_id: str) -> <PERSON><PERSON>[bool, str]:
    """将登录账号改为指定计划"""
    # 支持任意计划ID的通用方法
```

### 2. 异步支持
在 `WorkerThread` 类中添加了对应的操作支持：

```python
elif self.operation == "put_user_on_max_plan":
    success, message = self.api_client.put_user_on_max_plan()
    self.finished.emit(success, message, None)

elif self.operation == "put_user_on_plan":
    plan_id = self.kwargs.get('plan_id', 'orb_community_plan')
    success, message = self.api_client.put_user_on_plan(plan_id)
    self.finished.emit(success, message, None)
```

### 3. 用户界面
在"批量操作"标签页中添加了新按钮：

- **按钮标题**: "⭐ 切换到 Max 计划"
- **按钮描述**: "将当前登录账号切换到 Max 计划"
- **按钮颜色**: 青色渐变 (#06b6d4 到 #22d3ee)
- **位置**: 批量操作网格的第3行第1列

### 4. 事件处理
添加了对应的事件处理方法：

```python
def switch_to_max_plan(self):
    """切换到 Max 计划"""
    self.log_info("计划切换", "准备将当前账号切换到 Max 计划...")
    self.log_batch_operation("开始切换账号到 Max 计划")
    self.start_worker_thread("put_user_on_max_plan")
```

### 5. 配置更新
更新了默认配置中的认证信息：

- **Cookie**: 使用您提供的最新 cookie 字符串
- **Referer**: 更新为 `https://app.augmentcode.com/account/subscription`
- **其他请求头**: 保持与您提供的 fetch 请求一致

## API 调用详情

### 请求信息
- **URL**: `https://app.augmentcode.com/api/put-user-on-plan`
- **方法**: POST
- **请求头**: 使用配置文件中的标准请求头，包括您提供的最新 cookie
- **请求体**: 
  ```json
  {
    "planId": "orb_max_plan"
  }
  ```

### 响应处理
- **成功**: HTTP 200 状态码，显示成功消息
- **失败**: 其他状态码，显示错误信息和响应内容
- **异常**: 网络错误等异常情况的处理

## 使用方法

1. **启动应用**: 运行 `team_manager.py`
2. **配置API**: 确保配置中的 Cookie 和其他API信息正确
3. **切换标签页**: 点击"🔄 批量操作"标签页
4. **执行操作**: 点击"⭐ 切换到 Max 计划"按钮
5. **查看结果**: 在系统日志中查看操作结果

## 界面布局

批量操作标签页的按钮布局：

```
第1行: [🗑️ 删除未加入成员] [📧 删除邀请记录]
第2行: [🚫 删除所有未确认]   [🔄 切换到社区计划]
第3行: [⭐ 切换到 Max 计划]  [空位]
```

## 安全注意事项

⚠️ **重要提醒**:
- 此操作会实际改变您的账号计划
- 操作不可撤销，请谨慎使用
- 确保您有足够的权限执行此操作
- 建议在操作前确认当前账号状态
- Max Plan 可能涉及费用变更，请确认您的账单设置

## 日志记录

操作过程中会记录以下日志：
- 操作开始提示
- API调用结果（成功/失败）
- 详细的错误信息（如果失败）
- 操作完成状态

## 技术特性

- **异步执行**: 使用工作线程避免界面冻结
- **错误处理**: 完善的异常处理和错误提示
- **日志记录**: 详细的操作日志和状态跟踪
- **界面集成**: 与现有UI风格保持一致
- **配置支持**: 使用统一的配置管理系统
- **灵活性**: 支持通用的计划切换方法

## 测试验证

已通过完整的集成测试：
- ✅ API 方法正确添加
- ✅ 工作线程操作支持
- ✅ 配置信息更新
- ✅ UI 界面集成
- ✅ 事件处理正常

## 扩展性

通过新增的 `put_user_on_plan(plan_id)` 方法，可以轻松支持其他计划类型：
- `orb_community_plan` - 社区计划
- `orb_max_plan` - Max 计划
- 其他未来可能的计划类型

## 故障排除

如果操作失败，请检查：
1. 网络连接是否正常
2. Cookie 是否已过期
3. 账号是否有切换计划的权限
4. API 服务器是否可访问
5. 查看详细的错误日志信息

---

**版本**: 1.0  
**更新时间**: 2025-01-14  
**兼容性**: 与现有团队管理工具完全兼容
